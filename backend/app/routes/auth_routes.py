from flask import Blueprint
from ..controllers.signup_controller import register, confirm_email
from ..controllers.login_controller import login, verify_otp, toggle_2fa
from ..controllers.password_controller import send_reset_link, reset_password
from flask_jwt_extended import jwt_required

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

auth_bp.route('/register', methods=['POST'])(register)
auth_bp.route('/confirm-email', methods=['GET'])(confirm_email)
auth_bp.route('/login', methods=['POST'])(login)
auth_bp.route('/login/verify', methods=['POST'])(verify_otp)
auth_bp.route('/2fa-toggle', methods=['PATCH'])(jwt_required()(toggle_2fa))
auth_bp.route('/forgot-password', methods=['POST'])(send_reset_link)
auth_bp.route('/reset-password', methods=['POST'])(reset_password)
