"""
Scan Controller Module

Contrôleur unifié pour tous les outils de scan de vulnérabilités.
Gère OpenVAS, ZAP, Nikto et autres outils de scan.
"""

from flask import request, jsonify
from ..services.openvas_service import OpenVASService
from ..services.nessus_scan_service import NessusScanService


def get_available_tools():
    """Retourne la liste des outils de scan disponibles"""
    tools = {
        'openvas': {
            'name': 'OpenVAS',
            'description': 'Open Vulnerability Assessment Scanner',
            'status': 'available',
            'version': '21.4'
        },
        'nessus': {
            'name': 'Nessus',
            'description': 'Tenable Nessus Vulnerability Scanner',
            'status': 'available',
            'version': '10.0'
        },
        'zap': {
            'name': 'OWASP ZAP',
            'description': 'Web Application Security Scanner',
            'status': 'planned',
            'version': 'N/A'
        },
        'nikto': {
            'name': 'Nikto',
            'description': 'Web Server Scanner',
            'status': 'planned',
            'version': 'N/A'
        }
    }

    return jsonify({
        'tools': tools,
        'total_available': len([t for t in tools.values() if t['status'] == 'available']),
        'total_planned': len([t for t in tools.values() if t['status'] == 'planned'])
    }), 200


# ============================================================================
# OPENVAS ENDPOINTS
# ============================================================================

def get_openvas_port_lists():
    """Récupère les port lists OpenVAS disponibles"""
    try:
        openvas = OpenVASService()
        port_lists = openvas.get_port_lists()
        return jsonify({'port_lists': port_lists}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def get_openvas_configs():
    """Récupère les configurations de scan OpenVAS"""
    try:
        openvas = OpenVASService()
        configs = openvas.get_scan_configs()
        return jsonify({'configs': configs}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def create_openvas_target():
    """Crée une nouvelle cible OpenVAS"""
    try:
        data = request.get_json()
        if not data or 'name' not in data or 'hosts' not in data:
            return jsonify({'error': 'Missing required fields: name, hosts'}), 400

        openvas = OpenVASService()
        target_id = openvas.create_target(
            name=data['name'],
            hosts=data['hosts'],
            port_list_id=data.get('port_list_id')
        )

        return jsonify({
            'message': 'Target created successfully',
            'target_id': target_id
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def start_openvas_scan():
    """Démarre un scan OpenVAS"""
    try:
        data = request.get_json()
        if not data or 'target_hosts' not in data:
            return jsonify({'error': 'Missing required field: target_hosts'}), 400

        openvas = OpenVASService()
        task_id = openvas.start_scan(
            target_hosts=data['target_hosts'],
            scan_name=data.get('scan_name', 'PICA Scan')
        )

        return jsonify({
            'message': 'Scan started successfully',
            'task_id': task_id,
            'status': 'running'
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def get_openvas_scan_status(task_id):
    """Récupère le statut d'un scan OpenVAS"""
    try:
        openvas = OpenVASService()
        status = openvas.get_scan_status(task_id)
        return jsonify({
            'task_id': task_id,
            'status': status
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def get_openvas_scan_results(task_id):
    """Récupère les résultats d'un scan OpenVAS"""
    try:
        openvas = OpenVASService()
        results = openvas.get_scan_results(task_id)
        return jsonify({
            'task_id': task_id,
            'results': results
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


# ============================================================================
# NESSUS ENDPOINTS (Future implementation)
# ============================================================================

def start_nessus_scan():
    """Démarre un scan Nessus"""
    try:
        data = request.get_json()
        if not data or 'targets' not in data:
            return jsonify({'error': 'Missing required field: targets'}), 400

        nessus = NessusScanService()
        scan_id = nessus.start_scan(
            targets=data['targets'],
            scan_name=data.get('scan_name', 'PICA Nessus Scan')
        )

        return jsonify({
            'message': 'Nessus scan started successfully',
            'scan_id': scan_id,
            'status': 'running'
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def get_nessus_scan_status(scan_id):
    """Récupère le statut d'un scan Nessus"""
    try:
        nessus = NessusScanService()
        status = nessus.get_scan_status(scan_id)
        return jsonify({
            'scan_id': scan_id,
            'status': status
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def get_nessus_scan_results(scan_id):
    """Récupère les résultats d'un scan Nessus"""
    try:
        nessus = NessusScanService()
        results = nessus.get_scan_results(scan_id)
        return jsonify({
            'scan_id': scan_id,
            'results': results
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500