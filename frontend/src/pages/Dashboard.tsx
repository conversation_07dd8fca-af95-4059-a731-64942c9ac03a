import React from 'react';
import { ShieldCheck, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export default function Dashboard() {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2d004d] to-[#5f1a7c]">
      {/* Header */}
      <header className="bg-[#1a1a2e] shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <ShieldCheck className="w-8 h-8 text-violet-400 mr-3" />
              <h1 className="text-xl font-bold text-white">PICA Dashboard</h1>
            </div>
            
            <button
              onClick={handleLogout}
              className="flex items-center px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Déconnexion
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-4">
              Bienvenue sur PICA
            </h2>
            <p className="text-xl text-violet-200 mb-8">
              Plateforme Intégrée de Cybersécurité Avancée
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              {/* Card 1 */}
              <div className="bg-[#1a1a2e] rounded-lg p-6 shadow-xl">
                <div className="text-violet-400 text-3xl mb-4">🛡️</div>
                <h3 className="text-xl font-semibold mb-2">Scan de Vulnérabilités</h3>
                <p className="text-gray-300">
                  Analysez vos systèmes avec OpenVAS et Nessus
                </p>
              </div>

              {/* Card 2 */}
              <div className="bg-[#1a1a2e] rounded-lg p-6 shadow-xl">
                <div className="text-violet-400 text-3xl mb-4">📊</div>
                <h3 className="text-xl font-semibold mb-2">Rapports Détaillés</h3>
                <p className="text-gray-300">
                  Consultez des rapports complets de sécurité
                </p>
              </div>

              {/* Card 3 */}
              <div className="bg-[#1a1a2e] rounded-lg p-6 shadow-xl">
                <div className="text-violet-400 text-3xl mb-4">⚙️</div>
                <h3 className="text-xl font-semibold mb-2">Configuration</h3>
                <p className="text-gray-300">
                  Gérez vos paramètres et préférences
                </p>
              </div>
            </div>

            <div className="mt-12 bg-[#1a1a2e] rounded-lg p-8 shadow-xl">
              <h3 className="text-2xl font-bold mb-4">Statut du Système</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-400 rounded-md">
                  <span>OpenVAS</span>
                  <span className="text-green-400 font-semibold">✓ Opérationnel</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-400 rounded-md">
                  <span>Nessus</span>
                  <span className="text-green-400 font-semibold">✓ Opérationnel</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
