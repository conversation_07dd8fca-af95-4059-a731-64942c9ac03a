import React, { useEffect, useState } from 'react';
import { CheckCir<PERSON>, XCircle, Loader } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import axios from 'axios';

export default function EmailConfirmed() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    const confirmEmail = async () => {
      const token = searchParams.get('token');

      if (!token) {
        setStatus('error');
        setMessage('Token de confirmation manquant');
        return;
      }

      try {
        const response = await axios.get(`http://localhost:5000/auth/confirm-email?token=${token}`);
        setStatus('success');
        setMessage(response.data.msg || 'Email confirmé avec succès !');

        // Rediriger vers la page de connexion après 3 secondes
        setTimeout(() => navigate('/login'), 3000);
      } catch (error: any) {
        setStatus('error');
        setMessage(error?.response?.data?.msg || 'Erreur lors de la confirmation de l\'email');
      }
    };

    confirmEmail();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] px-4">
      <div className="max-w-md w-full bg-[#1a1a2e] rounded-lg p-8 shadow-xl text-white text-center">
        {status === 'loading' && (
          <>
            <Loader className="w-16 h-16 text-violet-400 mx-auto mb-4 animate-spin" />
            <h2 className="text-2xl font-semibold mb-2">Confirmation en cours...</h2>
            <p className="text-sm text-gray-300">Veuillez patienter</p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2 text-green-400">Email confirmé !</h2>
            <p className="text-sm text-gray-300 mb-4">{message}</p>
            <p className="text-xs text-violet-300">Redirection vers la page de connexion dans 3 secondes...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2 text-red-400">Erreur de confirmation</h2>
            <p className="text-sm text-gray-300 mb-6">{message}</p>
            <button
              onClick={() => navigate('/login')}
              className="px-6 py-2 bg-violet-600 hover:bg-violet-700 rounded-md transition-colors"
            >
              Retour à la connexion
            </button>
          </>
        )}
      </div>
    </div>
  );
}
